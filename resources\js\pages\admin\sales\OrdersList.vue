<template>
  <div class="space-y-6">
    <!-- Header -->
    <div class="bg-white shadow rounded-lg">
      <div class="px-4 py-5 sm:p-6">
        <div class="flex items-center justify-between">
          <div>
            <h1 class="text-2xl font-bold text-gray-900">Orders</h1>
            <p class="mt-1 text-sm text-gray-500">Manage customer orders and transactions</p>
          </div>
          <div class="flex items-center space-x-3">
            <Button variant="outline" size="sm" @click="exportOrders">
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4-4m0 0l-4 4m4-4v12"/>
              </svg>
              Export
            </Button>
            <Button variant="primary" @click="handleCreate">
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
              </svg>
              New Order
            </Button>
          </div>
        </div>
      </div>
    </div>

    <!-- Filters -->
    <div class="bg-white shadow rounded-lg">
      <div class="px-4 py-5 sm:p-6">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Select
            v-model="filters.status"
            placeholder="Filter by status"
            :options="statusOptions"
            @change="applyFilters"
          />
          <Select
            v-model="filters.dateRange"
            placeholder="Date range"
            :options="dateRangeOptions"
            @change="applyFilters"
          />
          <Input
            v-model="filters.customer"
            placeholder="Search customer..."
            @input="debouncedSearch"
            :loading="isSearching"
          />
          <Input
            v-model="filters.orderId"
            placeholder="Order ID..."
            @input="debouncedSearch"
            :loading="isSearching"
          />
        </div>
      </div>
    </div>

    <!-- Vue Good Table -->
    <div class="bg-white shadow rounded-lg overflow-hidden">
      <!-- Loading Overlay -->
      <div v-if="loading || isSearching" class="relative">
        <div class="absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center z-10">
          <div class="flex flex-col items-center">
            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mb-2"></div>
            <p class="text-sm text-gray-600">
              {{ isSearching ? 'Searching orders...' : 'Loading orders from server...' }}
            </p>
          </div>
        </div>
      </div>

      <VueGoodTable
        :columns="goodTableColumns"
        :rows="orders"
        :pagination-options="paginationOptions"
        :search-options="searchOptions"
        :sort-options="sortOptions"
        :loading="loading"
        styleClass="vgt-table striped bordered"
        @on-search="handleSearch"
      >
        <!-- Custom column templates -->
        <template #table-row="props">
          <span v-if="props.column.field === 'customer'">
            <div class="text-sm font-medium text-gray-900">{{ props.row.customer }}</div>
            <div class="text-sm text-gray-500">{{ props.row.customer_email }}</div>
          </span>
          <span v-else-if="props.column.field === 'items'">
            {{ props.row.sales_count }} item{{ props.row.sales_count === 1 ? '' : 's' }}
          </span>
          <span v-else-if="props.column.field === 'amount_total'">
            <div class="font-medium">${{ formatCurrency(props.row.amount_total) }}</div>
            <div v-if="props.row.discount > 0" class="text-xs text-green-600">
              -${{ formatCurrency(props.row.discount) }} discount
            </div>
          </span>
          <span v-else-if="props.column.field === 'status'">
            <span :class="getStatusClass(props.row.status)" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full">
              {{ props.row.status }}
            </span>
          </span>
          <span v-else-if="props.column.field === 'created_at'">
            {{ formatDate(props.row.created_at) }}
          </span>
          <span v-else-if="props.column.field === 'actions'">
            <div class="flex justify-end space-x-2">
              <Button variant="ghost" size="sm" @click="viewOrder(props.row)">
                View
              </Button>
              <Button variant="ghost" size="sm" @click="editOrder(props.row)">
                Edit
              </Button>
              <Button
                v-if="props.row.status_id !== 11"
                variant="ghost"
                size="sm"
                @click="processOrder(props.row)"
              >
                Process
              </Button>
            </div>
          </span>
          <span v-else>
            {{ props.formattedRow[props.column.field] }}
          </span>
        </template>

        <!-- Empty state template -->
        <template #emptystate>
          <div class="text-center py-12">
            <div class="text-gray-400 mb-4">
              <svg class="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2M4 13h2m13-8l-4 4-4-4m0 0V3" />
              </svg>
            </div>
            <h3 class="text-lg font-medium text-gray-900 mb-2">No orders found</h3>
            <p class="text-gray-500 mb-4">Orders will appear here when customers make purchases.</p>
            <Button variant="primary" @click="handleCreate">
              New Order
            </Button>
          </div>
        </template>
      </VueGoodTable>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed, watch } from 'vue';
import { useRouter } from 'vue-router';
import { VueGoodTable, type VgtColumn, type VgtPaginationOptions, type VgtSearchOptions, type VgtSortOptions } from 'vue-good-table-next';
import 'vue-good-table-next/dist/vue-good-table-next.css';
import { Button, Select, Input } from '@/components/ui';
import { useApi } from '@/composables/useApi';



interface Order {
  id: number;
  order_id: string;
  customer: string;
  customer_email: string;
  customer_id: number;
  description: string;
  sub_total: number;
  amount_total: number;
  amount_paid: number;
  discount: number;
  vat: number;
  status: string;
  status_id: number;
  prepared_by: string;
  approved_by_name: string;
  branch: string;
  branch_id: number;
  sales_count: number;
  payments_count: number;
  created_at: string;
  updated_at: string;
}

// Router
const router = useRouter();

// API setup
const { data: apiData, isLoading: loading, execute: fetchOrders } = useApi<any>('/admin/orders');

// State
const currentPage = ref(1);
const totalPages = ref(1);
const totalItems = ref(0);
const perPage = ref(20);
const isSearching = ref(false);
const searchDebounceTimer = ref<NodeJS.Timeout | null>(null);

// Filters
const filters = ref({
  status: '',
  dateRange: '',
  customer: '',
  orderId: ''
});

// Options
const statusOptions = [
  { value: '', label: 'All Statuses' },
  { value: '1', label: 'Active' },
  { value: '11', label: 'Completed' },
  { value: '13', label: 'Cancelled' }
];

const dateRangeOptions = [
  { value: '', label: 'All Time' },
  { value: 'today', label: 'Today' },
  { value: 'week', label: 'This Week' },
  { value: 'month', label: 'This Month' },
  { value: 'quarter', label: 'This Quarter' }
];

// Vue Good Table columns configuration
const goodTableColumns = computed<VgtColumn[]>(() => [
  {
    label: 'Order ID',
    field: 'order_id',
    sortable: true,
    width: '120px',
    title: 'Unique order identifier - Click to sort by order ID'
  },
  {
    label: 'Customer Information',
    field: 'customer',
    sortable: true,
    width: '200px',
    title: 'Customer name and email address - Click to sort by customer name'
  },
  {
    label: 'Item Count',
    field: 'items',
    sortable: false,
    width: '100px',
    title: 'Total number of items in this order'
  },
  {
    label: 'Order Total',
    field: 'amount_total',
    sortable: true,
    type: 'number',
    width: '120px',
    title: 'Total order amount including discounts - Click to sort by amount'
  },
  {
    label: 'Order Status',
    field: 'status',
    sortable: true,
    width: '120px',
    title: 'Current processing status of the order - Click to sort by status'
  },
  {
    label: 'Order Date',
    field: 'created_at',
    sortable: true,
    width: '140px',
    title: 'Date and time when the order was created - Click to sort by date'
  },
  {
    label: 'Available Actions',
    field: 'actions',
    sortable: false,
    width: '180px',
    title: 'Actions you can perform on this order (View, Edit, Process)'
  }
]);

// Vue Good Table options
const paginationOptions: VgtPaginationOptions = {
  enabled: true,
  mode: 'records',
  perPage: perPage.value,
  position: 'bottom',
  perPageDropdown: [10, 20, 50, 100],
  dropdownAllowAll: false,
  setCurrentPage: 1,
  nextLabel: 'Next',
  prevLabel: 'Prev',
  rowsPerPageLabel: 'Rows per page',
  ofLabel: 'of',
  pageLabel: 'page',
  allLabel: 'All'
};

const searchOptions: VgtSearchOptions = {
  enabled: true,
  trigger: 'keyup',
  skipDiacritics: true,
  placeholder: 'Search orders by ID, customer, status... (auto-search as you type)',
  searchFn: (searchTerm: any, row: any) => {
    try {
      // Custom search function for better matching
      if (!searchTerm || typeof searchTerm !== 'string') return true;

      const term = searchTerm.toLowerCase().trim();
      if (!term) return true;

      const searchableFields = [
        row.order_id?.toString().toLowerCase(),
        row.customer?.toLowerCase(),
        row.customer_email?.toLowerCase(),
        row.status?.toLowerCase(),
        row.amount_total?.toString().toLowerCase(),
        row.description?.toLowerCase()
      ].filter(Boolean); // Remove null/undefined values

      return searchableFields.some(field => field.includes(term));
    } catch (error) {
      console.error('Search function error:', error);
      return true; // Return true to show all rows if search fails
    }
  }
};

const sortOptions: VgtSortOptions = {
  enabled: true,
  initialSortBy: { field: 'created_at', type: 'desc' }
};

// Computed
const orders = computed(() => {
  return apiData.value?.data || [];
});

// Update pagination when API data changes
watch(apiData, (newData) => {
  if (newData) {
    currentPage.value = newData.current_page || 1;
    totalPages.value = newData.last_page || 1;
    totalItems.value = newData.total || 0;
    perPage.value = newData.per_page || 20;
  }
}, { immediate: true });

// Methods
const loadOrders = async () => {
  const params: any = {
    page: currentPage.value,
    per_page: perPage.value
  };

  // Apply filters
  if (filters.value.status) params.status = filters.value.status;
  if (filters.value.customer) params.customer = filters.value.customer;
  if (filters.value.orderId) params.order_id = filters.value.orderId;
  if (filters.value.dateRange) params.date_range = filters.value.dateRange;

  try {
    // Show loading state
    console.log('Loading orders from server...', params);
    await fetchOrders(params);
    console.log('Orders loaded successfully');
  } catch (err) {
    console.error('Error loading orders:', err);
    // You could add toast notifications here for better user feedback
  }
};

const handleCreate = () => {
  router.push('/admin-spa/sales/orders/create');
};

const applyFilters = () => {
  currentPage.value = 1;
  loadOrders();
};

const debouncedSearch = () => {
  // Clear existing timer
  if (searchDebounceTimer.value) {
    clearTimeout(searchDebounceTimer.value);
  }

  // Set searching state
  isSearching.value = true;

  // Set new timer
  searchDebounceTimer.value = setTimeout(() => {
    applyFilters();
    isSearching.value = false;
  }, 500); // 500ms delay
};

const handleSearch = (params: any) => {
  // Handle search events from VueGoodTable
  console.log('Search triggered:', params);
  isSearching.value = true;

  // Simulate search delay
  setTimeout(() => {
    isSearching.value = false;
  }, 300);
};

const exportOrders = () => {
  console.log('Exporting orders...');
};

const viewOrder = (order: Order) => {
  router.push(`/admin-spa/sales/orders/${order.id}`);
};

const editOrder = (order: Order) => {
  router.push(`/admin-spa/sales/orders/${order.id}/edit`);
};

const processOrder = (order: Order) => {
  console.log('Processing order:', order.id);
  // Implement order processing logic
};

// Utility functions
const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('en-US', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(amount);
};

const formatDate = (dateString: string) => {
  try {
    if (!dateString) return '-';

    // Handle different date formats
    const date = new Date(dateString);

    // Check if date is valid
    if (isNaN(date.getTime())) {
      console.warn('Invalid date format:', dateString);
      return dateString; // Return original string if parsing fails
    }

    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  } catch (error) {
    console.error('Error formatting date:', error, dateString);
    return dateString; // Return original string if error occurs
  }
};

const getStatusClass = (status: string) => {
  const statusClasses: Record<string, string> = {
    'Active': 'bg-blue-100 text-blue-800',
    'Completed': 'bg-green-100 text-green-800',
    'Cancelled': 'bg-red-100 text-red-800',
    'Pending': 'bg-yellow-100 text-yellow-800',
    'Processing': 'bg-purple-100 text-purple-800'
  };
  return statusClasses[status] || 'bg-gray-100 text-gray-800';
};

// Watch for filter changes
watch(filters, () => {
  applyFilters();
}, { deep: true });

// Lifecycle
onMounted(() => {
  loadOrders();
});

onUnmounted(() => {
  // Cleanup debounce timer
  if (searchDebounceTimer.value) {
    clearTimeout(searchDebounceTimer.value);
  }
});
</script>
